
import ModularTabNavigation from "@/components/ModularTabNavigation";
import ModularChartRenderer from "@/components/ModularChartRenderer";
import { useEffect, useState } from "react";
import axios from "axios";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "react-router-dom";
import crypto from "crypto-js";
import { getClientConfiguration, getEnabledTabs } from "@/config/clientConfigurations";
import { ClientData } from "@/types/environment-data";

const Index = () => {
  const [activeView, setActiveView] = useState("environment-new");
  const [selectedMetric, setSelectedMetric] = useState("emissions");

  const [locationData, setLocationData] = useState<any[]>([]);
  const [supplierData, setSupplierData] = useState<any[]>([]);
  const [clientId, setClientId] = useState<string>("");
  const [clientConfig, setClientConfig] = useState(getClientConfiguration(""));
  const params = useLocation()
  const [param] = useState(params.search?.slice(1))
  console.log(params.search?.slice(1))
  const SECRET_KEY = "e!sq6esgdash1";
  function decryptNumber(encrypted) {
    var bytes = crypto.AES.decrypt(encrypted, SECRET_KEY);
    var originalText = bytes.toString(crypto.enc.Utf8);
    return originalText
  }
  function encryptNumber(number, secret) {
    return crypto.AES.encrypt(number, SECRET_KEY).toString();  // Return IV and encrypted text
  }

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const fetchSupplierData: any = async (
    category: string,
    supplier: string,
    location: string,
    indicator: number[],
    startDate: string,
    endDate: string
  ) => {
    try {
      // const response = await axios.post(
      //   "https://api.eisqr.com/user-profiles/289/get-dcf-status-by-indicator",
      //   {
      //     indicatorId: indicator,
      //     year: {
      //       startMonth: startDate,
      //       endMonth: endDate,
      //     },
      //   }
      // );

      // if (response.data) setIsLoading(false);
      return []
    } catch (err) {
      console.warn("error:", err);
    }
  };
  useEffect(() => {
    // console.log(encryptNumber(JSON.stringify({ userId: 101, adminId: 291 }), SECRET_KEY))
    if (param) {
      const { userId, adminId } = JSON.parse(decryptNumber(param))
      console.log(userId, adminId)
      setClientId(adminId.toString())

      // Get client configuration and set default active view
      const config = getClientConfiguration(adminId.toString())
      setClientConfig(config)

      // Set the first enabled tab as the default active view
      const enabledTabs = getEnabledTabs(adminId.toString())
      if (enabledTabs.length > 0) {
        setActiveView(enabledTabs[0].id)
      }
    }
  }, [param])

  useEffect(() => {
    const loadAllData = async () => {
      try {
        const [supplierResponse, supplierResponse2, supplierResponse3] =
          await Promise.all([
            fetchSupplierData(
              "category",
              "supplier",
              "location",
              [162, 163],
              `Apr-2023`,
              `May-2026`
            ),
            fetchSupplierData(
              "category",
              "supplier",
              "location",
              [202, 490, 800],
              `Apr-2023`,
              `May-2026`
            ),
            fetchSupplierData(
              "category",
              "supplier",
              "location",
              [170, 171, 172, 173, 175, 177, 178],
              `Apr-2023`,
              `May-2026`
            ),
          ]);

        const locationData = await fetchLocationData("all");
        setLocationData(locationData);

        const allData = [
          ...(supplierResponse || []),
          ...(supplierResponse2 || []),
          ...(supplierResponse3 || []),
        ];

        setSupplierData(allData);
      } catch (err) {
        console.error("Error loading initial data:", err);
      }
    };

    loadAllData();
  }, []);

  return (

    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* <Header /> */}

      {param ? <div>
        {/* Main Content */}
        {supplierData.length ? (
          <main className="p-6 space-y-8 overflow-x-hidden">
            {/* Chart-Centric Main Content */}
              {/* Only show tab navigation if client has multiple tabs or filters enabled */}
              {(getEnabledTabs(clientId).length > 1 || clientConfig.features.filters) && (
                <ModularTabNavigation
                  clientId={clientId}
                  activeView={activeView}
                  onViewChange={setActiveView}
                  selectedMetric={selectedMetric}
                  onMetricChange={setSelectedMetric}
                  suppliersData={supplierData.filter(
                    (item) =>
                      item.indicatorId == 162 ||
                      item.indicatorId == 163 ||
                      item.indicatorId == 164
                  )}
                />
              )}
              {/* Render charts based on client configuration */}
              <ModularChartRenderer
                clientId={clientId}
                activeView={activeView}
                supplierData={supplierData}
                locationData={locationData}
              />
          </main>
        ) : (
          <div className="p-6">
            <Skeleton className="h-[200px] w-[100vw] rounded-full" />
          </div>
        )}
      </div>
        : <div></div>
      }
    </div>

  );
};

export default Index;
