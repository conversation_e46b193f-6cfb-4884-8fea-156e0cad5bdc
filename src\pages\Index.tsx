
import ClientAwareChartNavigation from "@/components/ClientAwareChartNavigation";
import ChartNavigation from "@/components/charts/ChartNavigation";
import { ScopeEmissionsBarChart } from "@/components/charts/ScopeEmissionsBarChart";
import { EnvironmentScopeEmissionsBarChart } from "@/components/charts/EnvironmentScopeEmissionsBarChart";
import { Scope3EmissionsByLocationChart } from "@/components/charts/Scope3EmissionsByLocationChart";
import { EnvironmentScope3EmissionsByLocationChart } from "@/components/charts/EnvironmentScope3EmissionsByLocationChart";
import { RenewableNonRenewableEnergyChart } from "@/components/charts/RenewableNonRenewableEnergyChart";
import { RenewableEnergyBreakdownChart } from "@/components/charts/RenewableEnergyBreakdownChart";
import { NonRenewableEnergyBreakdownChart } from "@/components/charts/NonRenewableEnergyBreakdownChart";
import { WasteManagementChart } from "@/components/charts/WasteManagementChart";
import { HazardousWasteDisposedChart } from "@/components/charts/HazardousWasteDisposedChart";
import { NonHazardousWasteDisposedChart } from "@/components/charts/NonHazardousWasteDisposedChart";
import { WaterWithdrawalChart } from "@/components/charts/WaterWithdrawalChart";
import { WaterConsumptionChart } from "@/components/charts/WaterConsumptionChart";
import { WaterDischargeChart } from "@/components/charts/WaterDischargeChart";
import { SafetyLTIFChart } from "@/components/charts/SafetyLTIFChart";
import { EnergyEmissionsRatingsChart, Scope2EmissionsNalagarhChart, Scope1EmissionsHosurChart, Scope2EmissionsHosurChart } from "@/components/charts/EnergyEmissionsRatingsChart";
import { useEffect, useState } from "react";
import axios from "axios";
import { Skeleton } from "@/components/ui/skeleton";
import { useLocation } from "react-router-dom";
import crypto from "crypto-js";
import { getSimpleClientConfiguration, getEnabledTabs, isTabEnabled } from "@/config/clientConfigurations";
import { ClientData } from "@/types/environment-data";

const Index = () => {
  const [activeView, setActiveView] = useState("environment-new");
  const [selectedMetric, setSelectedMetric] = useState("emissions");

  const [locationData, setLocationData] = useState<any[]>([]);
  const [supplierData, setSupplierData] = useState<any[]>([]);
  const [clientId, setClientId] = useState<string>("");
  const [clientConfig, setClientConfig] = useState(getSimpleClientConfiguration(""));
  const params = useLocation()
  const [param] = useState(params.search?.slice(1))
  console.log(params.search?.slice(1))
  const SECRET_KEY = "e!sq6esgdash1";
  function decryptNumber(encrypted) {
    var bytes = crypto.AES.decrypt(encrypted, SECRET_KEY);
    var originalText = bytes.toString(crypto.enc.Utf8);
    return originalText
  }
  function encryptNumber(number, secret) {
    return crypto.AES.encrypt(number, SECRET_KEY).toString();  // Return IV and encrypted text
  }

  const fetchLocationData: any = async (location: string) => {
    try {
      const response = await axios.get(
        "https://api.eisqr.com/user-profiles/289/location-ones?filter=%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationTwos%22%2C%22scope%22%3A%7B%22include%22%3A%5B%7B%22relation%22%3A%22locationThrees%22%7D%5D%7D%7D%5D%7D"
      );

      return response.data;
    } catch (err) {
      console.warn("error:", err);
    }
  };

  const fetchSupplierData: any = async (
    category: string,
    supplier: string,
    location: string,
    indicator: number[],
    startDate: string,
    endDate: string
  ) => {
    try {
      // const response = await axios.post(
      //   "https://api.eisqr.com/user-profiles/289/get-dcf-status-by-indicator",
      //   {
      //     indicatorId: indicator,
      //     year: {
      //       startMonth: startDate,
      //       endMonth: endDate,
      //     },
      //   }
      // );

      // if (response.data) setIsLoading(false);
      return []
    } catch (err) {
      console.warn("error:", err);
    }
  };
  useEffect(() => {
    console.log(encryptNumber(JSON.stringify({ userId: 101, adminId: 289 }), SECRET_KEY))
    if (param) {
      const { userId, adminId } = JSON.parse(decryptNumber(param))
      console.log(userId, adminId)
      setClientId(adminId.toString())

      // Get client configuration and set default active view
      const config = getSimpleClientConfiguration(adminId.toString())
      setClientConfig(config)

      // Set the first enabled tab as the default active view
      const enabledTabs = getEnabledTabs(adminId.toString())
      if (enabledTabs.length > 0) {
        setActiveView(enabledTabs[0].id)
      }
    }
  }, [param])

  useEffect(() => {
    const loadAllData = async () => {
      try {
        const [supplierResponse, supplierResponse2, supplierResponse3] =
          await Promise.all([
            fetchSupplierData(
              "category",
              "supplier",
              "location",
              [162, 163],
              `Apr-2023`,
              `May-2026`
            ),
            fetchSupplierData(
              "category",
              "supplier",
              "location",
              [202, 490, 800],
              `Apr-2023`,
              `May-2026`
            ),
            fetchSupplierData(
              "category",
              "supplier",
              "location",
              [170, 171, 172, 173, 175, 177, 178],
              `Apr-2023`,
              `May-2026`
            ),
          ]);

        const locationData = await fetchLocationData("all");
        setLocationData(locationData);

        const allData = [
          ...(supplierResponse || []),
          ...(supplierResponse2 || []),
          ...(supplierResponse3 || []),
        ];

        setSupplierData(allData);
      } catch (err) {
        console.error("Error loading initial data:", err);
      }
    };

    loadAllData();
  }, []);

  return (

    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* <Header /> */}

      {param ? <div>
        {/* Main Content */}
        {supplierData.length ? (
          <main className="p-6 space-y-8 overflow-x-hidden">
            {/* Chart-Centric Main Content */}
              {/* Client-aware navigation and charts */}
              <ClientAwareChartNavigation
                clientId={clientId}
                activeView={activeView}
                onViewChange={setActiveView}
                selectedMetric={selectedMetric}
                onMetricChange={setSelectedMetric}
                suppliersData={supplierData.filter(
                  (item) =>
                    item.indicatorId == 162 ||
                    item.indicatorId == 163 ||
                    item.indicatorId == 164
                )}
              />

              {/* Render existing charts based on active view and client permissions */}
              {isTabEnabled(clientId, activeView) && (
                <>
                  {activeView === "environment-new" && (
                    <div className="mt-10">
                      <EnergyEmissionsRatingsChart region="Global" />
                      <Scope2EmissionsNalagarhChart />
                      <Scope1EmissionsHosurChart />
                      <Scope2EmissionsHosurChart />
                    </div>
                  )}

                  {activeView !== "environment-new" && (
                    <>
                      {/* Energy and Emissions Section */}
                      <div className="mt-10">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          {activeView === "environment"
                            ? "Climate Change and GHG Emissions"
                            : "Energy and Emissions"}
                        </h3>
                        <p className="text-gray-600 mb-4">
                          (Energy mix and carbon emissions across the organization)
                        </p>
                        <div className="mb-2">
                          <h4 className="text-lg font-semibold text-gray-800">
                            Scope 1+2+3 Emissions
                          </h4>
                          <p className="text-gray-500 text-sm mb-4">
                            (Direct emissions from operations that are owned or controlled
                            by the organization, indirect emissions from the consumption
                            of purchased electricity, steam, heating, or cooling generated
                            off-site but used by the organization — all measured in metric
                            tons of CO₂ equivalent [tCO₂e].)
                          </p>

                          {activeView === "environment" ? (
                            <EnvironmentScopeEmissionsBarChart />
                          ) : (
                            <ScopeEmissionsBarChart
                              suppliersData={supplierData.filter(
                                (item) =>
                                  item.indicatorId !== 202 &&
                                  item.indicatorId !== 800 &&
                                  item.indicatorId !== 490
                              )}
                            />
                          )}
                        </div>
                      </div>

                      {/* Scope 3 Emissions by Location Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          Scope 3 Emissions by Location
                        </h3>
                        <p className="text-gray-600 mb-4">
                          (Emissions across various categories)
                        </p>
                        {activeView === "environment" ? (
                          <EnvironmentScope3EmissionsByLocationChart />
                        ) : (
                          <Scope3EmissionsByLocationChart
                            suppliersData={supplierData.filter(
                              (item) =>
                                item.indicatorId !== 202 &&
                                item.indicatorId !== 800 &&
                                item.indicatorId !== 161 &&
                                item.indicatorId !== 162 &&
                                item.indicatorId !== 163 &&
                                item.indicatorId !== 490
                            )}
                          />
                        )}
                      </div>

                      {/* Renewable and Non-Renewable Energy Consumption Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          Renewable and Non-Renewable Energy Consumption
                        </h3>
                        <RenewableNonRenewableEnergyChart
                          region="Global"
                          suppliersData={supplierData.filter(
                            (item) => item.indicatorId == 163
                          )}
                          enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                          useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                        />
                      </div>

                      {/* Energy Consumption Breakdown Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-6">
                          Energy Consumption Breakdown
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-800 mb-2">
                              Renewable Energy Consumption
                            </h4>
                            <RenewableEnergyBreakdownChart
                              region="Global"
                              suppliersData={supplierData.filter(
                                (item) => item.indicatorId == 163
                              )}
                              enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                              useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                            />
                          </div>
                          <div>
                            <h4 className="text-lg font-semibold text-gray-800 mb-2">
                              Non-Renewable Energy Consumption
                            </h4>
                            <NonRenewableEnergyBreakdownChart
                              region="Global"
                              suppliersData={supplierData.filter(
                                (item) => item.indicatorId == 163
                              )}
                              enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                              useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Waste Management Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          Waste Management
                        </h3>
                        <p className="text-gray-600 mb-4">
                          (Managing the collection and disposal of waste across the
                          organization)
                        </p>
                        <WasteManagementChart
                          region="Global"
                          enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                          useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                        />
                      </div>

                      {/* Hazardous Waste Disposed Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          Hazardous Waste Disposed
                        </h3>
                        <HazardousWasteDisposedChart
                          region="Global"
                          enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                          useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                        />
                      </div>

                      {/* Non-Hazardous Waste Disposed Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          Non-Hazardous Waste Disposed
                        </h3>
                        <NonHazardousWasteDisposedChart
                          region="Global"
                          enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                          useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                        />
                      </div>

                      {/* Water Stewardship Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-1">
                          Water Stewardship
                        </h3>
                        <p className="text-gray-600 mb-4">
                          (Management of water withdrawal and disposal)
                        </p>
                        <WaterWithdrawalChart
                          region="Global"
                          suppliersData={supplierData.filter(
                            (item) => item.indicatorId == 202
                          )}
                          enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                        />
                      </div>

                      {/* Water Consumption and Water Discharge Section */}
                      <div className="mt-16">
                        <h3 className="text-2xl font-bold text-gray-900 mb-6">
                          Water Consumption and Water Discharge
                        </h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                          <div>
                            <h4 className="text-lg font-semibold text-gray-800 mb-2">
                              Water Consumption
                            </h4>
                            <WaterConsumptionChart
                              region="Global"
                              suppliersData={supplierData.filter(
                                (item) => item.indicatorId == 800
                              )}
                              enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                              useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                            />
                          </div>
                          <div>
                            <h4 className="text-lg font-semibold text-gray-800 mb-2">
                              Water Discharge
                            </h4>
                            <WaterDischargeChart
                              region="Global"
                              suppliersData={supplierData.filter(
                                (item) => item.indicatorId == 490
                              )}
                              enableDrilldown={activeView === "environment" && clientConfig.features.drilldown}
                              useLocationFilter={activeView === "environment" && clientConfig.features.locationFilter}
                            />
                          </div>
                        </div>
                      </div>

                      {/* Safety Section */}
                      {activeView !== "environment" && (
                        <div className="mt-16">
                          <h3 className="text-2xl font-bold text-gray-900 mb-1">
                            Safety
                          </h3>
                          <p className="text-gray-600 mb-2">
                            (Employee safety performance across the organization)
                          </p>
                          <h4 className="text-lg font-semibold text-gray-800 mb-1">
                            LTIF (Lost Time Injury Frequency Rate)
                          </h4>
                          <p className="text-gray-500 text-sm mb-4">
                            Number of lost time injuries per million hours worked
                          </p>
                          <SafetyLTIFChart region="Global" />
                        </div>
                      )}
                    </>
                  )}
                </>
              )}
          </main>
        ) : (
          <div className="p-6">
            <Skeleton className="h-[200px] w-[100vw] rounded-full" />
          </div>
        )}
      </div>
        : <div></div>
      }
    </div>

  );
};

export default Index;
