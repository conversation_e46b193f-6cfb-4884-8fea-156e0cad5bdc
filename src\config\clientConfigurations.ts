import { ClientConfig } from "@/types/environment-data";

// Default client configuration template
const defaultClientConfig: Omit<ClientConfig, 'clientId' | 'adminId' | 'name'> = {
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: "BarChart3",
      enabled: true,
      order: 1,
      sections: [
        {
          id: "energy-emissions",
          title: "Energy and Emissions",
          description: "(Energy mix and carbon emissions across the organization)",
          enabled: true,
          order: 1,
          charts: [
            { id: "scope-emissions", component: "ScopeEmissionsBarChart", enabled: true, order: 1 },
            { id: "scope3-location", component: "Scope3EmissionsByLocationChart", enabled: true, order: 2 },
            { id: "renewable-energy", component: "RenewableNonRenewableEnergyChart", enabled: true, order: 3 },
            { id: "renewable-breakdown", component: "RenewableEnergyBreakdownChart", enabled: true, order: 4 },
            { id: "non-renewable-breakdown", component: "NonRenewableEnergyBreakdownChart", enabled: true, order: 5 }
          ]
        },
        {
          id: "waste-management",
          title: "Waste Management",
          description: "(Managing the collection and disposal of waste across the organization)",
          enabled: true,
          order: 2,
          charts: [
            { id: "waste-management", component: "WasteManagementChart", enabled: true, order: 1 },
            { id: "hazardous-waste", component: "HazardousWasteDisposedChart", enabled: true, order: 2 },
            { id: "non-hazardous-waste", component: "NonHazardousWasteDisposedChart", enabled: true, order: 3 }
          ]
        },
        {
          id: "water-stewardship",
          title: "Water Stewardship",
          description: "(Management of water withdrawal and disposal)",
          enabled: true,
          order: 3,
          charts: [
            { id: "water-withdrawal", component: "WaterWithdrawalChart", enabled: true, order: 1 },
            { id: "water-consumption", component: "WaterConsumptionChart", enabled: true, order: 2 },
            { id: "water-discharge", component: "WaterDischargeChart", enabled: true, order: 3 }
          ]
        },
        {
          id: "safety",
          title: "Safety",
          description: "(Employee safety performance across the organization)",
          enabled: true,
          order: 4,
          charts: [
            { id: "safety-ltif", component: "SafetyLTIFChart", enabled: true, order: 1 }
          ]
        }
      ]
    },
    {
      id: "environment",
      label: "Environment",
      icon: "Leaf",
      enabled: true,
      order: 2,
      sections: [
        {
          id: "climate-change",
          title: "Climate Change and GHG Emissions",
          description: "(Energy mix and carbon emissions across the organization)",
          enabled: true,
          order: 1,
          charts: [
            { id: "environment-scope-emissions", component: "EnvironmentScopeEmissionsBarChart", enabled: true, order: 1 },
            { id: "environment-scope3-location", component: "EnvironmentScope3EmissionsByLocationChart", enabled: true, order: 2 }
          ]
        }
      ]
    },
    {
      id: "environment-new",
      label: "Environment (New)",
      icon: "Leaf",
      enabled: true,
      order: 3,
      sections: [
        {
          id: "esg-ratings",
          title: "ESG Ratings",
          enabled: true,
          order: 1,
          charts: [
            { id: "energy-emissions-ratings", component: "EnergyEmissionsRatingsChart", enabled: true, order: 1 },
            { id: "scope2-nalagarh", component: "Scope2EmissionsNalagarhChart", enabled: true, order: 2 },
            { id: "scope1-hosur", component: "Scope1EmissionsHosurChart", enabled: true, order: 3 },
            { id: "scope2-hosur", component: "Scope2EmissionsHosurChart", enabled: true, order: 4 }
          ]
        }
      ]
    }
  ],
  features: {
    sidebar: false,
    filters: true,
    drilldown: true,
    locationFilter: true
  }
};

// Client-specific configurations
export const clientConfigurations: Record<string, ClientConfig> = {
  // Client 101 - Full access
  "101": {
    clientId: "101",
    adminId: "291",
    name: "Full Access Client",
    ...defaultClientConfig,
    features: {
      sidebar: false,
      filters: true,
      drilldown: true,
      locationFilter: true
    }
  },

  // Client 102 - Limited to Environment tabs only
  "102": {
    clientId: "102",
    adminId: "292",
    name: "Environment Only Client",
    tabs: [
      {
        id: "environment",
        label: "Environment",
        icon: "Leaf",
        enabled: true,
        order: 1,
        sections: defaultClientConfig.tabs.find(t => t.id === "environment")?.sections || []
      },
      {
        id: "environment-new",
        label: "Environment (New)",
        icon: "Leaf",
        enabled: true,
        order: 2,
        sections: defaultClientConfig.tabs.find(t => t.id === "environment-new")?.sections || []
      }
    ],
    features: {
      sidebar: false,
      filters: true,
      drilldown: false,
      locationFilter: false
    }
  },

  // Client 103 - Overview only with limited charts
  "103": {
    clientId: "103",
    adminId: "293",
    name: "Overview Only Client",
    tabs: [
      {
        id: "overview",
        label: "Overview",
        icon: "BarChart3",
        enabled: true,
        order: 1,
        sections: [
          {
            id: "energy-emissions",
            title: "Energy and Emissions",
            description: "(Energy mix and carbon emissions across the organization)",
            enabled: true,
            order: 1,
            charts: [
              { id: "scope-emissions", component: "ScopeEmissionsBarChart", enabled: true, order: 1 },
              { id: "renewable-energy", component: "RenewableNonRenewableEnergyChart", enabled: true, order: 2 }
            ]
          }
        ]
      }
    ],
    features: {
      sidebar: false,
      filters: false,
      drilldown: false,
      locationFilter: false
    }
  }
};

// Function to get client configuration
export const getClientConfiguration = (clientId: string): ClientConfig => {
  return clientConfigurations[clientId] || {
    clientId: clientId,
    adminId: "default",
    name: "Default Client",
    ...defaultClientConfig
  };
};

// Function to check if a tab is enabled for a client
export const isTabEnabled = (clientId: string, tabId: string): boolean => {
  const config = getClientConfiguration(clientId);
  const tab = config.tabs.find(t => t.id === tabId);
  return tab?.enabled || false;
};

// Function to get enabled tabs for a client
export const getEnabledTabs = (clientId: string) => {
  const config = getClientConfiguration(clientId);
  return config.tabs.filter(tab => tab.enabled).sort((a, b) => a.order - b.order);
};

// Function to get enabled sections for a tab
export const getEnabledSections = (clientId: string, tabId: string) => {
  const config = getClientConfiguration(clientId);
  const tab = config.tabs.find(t => t.id === tabId);
  return tab?.sections?.filter(section => section.enabled).sort((a, b) => a.order - b.order) || [];
};

// Function to get enabled charts for a section
export const getEnabledCharts = (clientId: string, tabId: string, sectionId: string) => {
  const sections = getEnabledSections(clientId, tabId);
  const section = sections.find(s => s.id === sectionId);
  return section?.charts?.filter(chart => chart.enabled).sort((a, b) => a.order - b.order) || [];
};
