import { ClientConfig } from "@/types/environment-data";

// Simple tab visibility configuration
interface SimpleClientConfig {
  clientId: string;
  adminId: string;
  name: string;
  enabledTabs: string[]; // Array of tab IDs that should be visible
  features: {
    sidebar: boolean;
    filters: boolean;
    drilldown: boolean;
    locationFilter: boolean;
  };
}

// All available tabs (existing tabs from your current system)
const allAvailableTabs = [
  "overview",
  "environment",
  "environment-new"
];

// Simple client configurations (using adminId as the key)
export const simpleClientConfigurations: Record<string, SimpleClientConfig> = {
  // Admin 289 - Full access (the main valid admin)
  "289": {
    clientId: "289",
    adminId: "289",
    name: "Main Admin - Full Access",
    enabledTabs: ["overview", "environment", "environment-new"],
    features: {
      sidebar: false,
      filters: true,
      drilldown: true,
      locationFilter: true
    }
  },

  // Admin 291 - Full access
  "291": {
    clientId: "291",
    adminId: "291",
    name: "Full Access Client",
    enabledTabs: ["overview", "environment", "environment-new"],
    features: {
      sidebar: false,
      filters: true,
      drilldown: true,
      locationFilter: true
    }
  },

  // Admin 292 - Environment tabs only
  "292": {
    clientId: "292",
    adminId: "292",
    name: "Environment Only Client",
    enabledTabs: ["environment", "environment-new"],
    features: {
      sidebar: false,
      filters: true,
      drilldown: false,
      locationFilter: false
    }
  },

  // Admin 293 - Overview only
  "293": {
    clientId: "293",
    adminId: "293",
    name: "Overview Only Client",
    enabledTabs: ["overview"],
    features: {
      sidebar: false,
      filters: false,
      drilldown: false,
      locationFilter: false
    }
  }
};

// Function to get simple client configuration
export const getSimpleClientConfiguration = (clientId: string): SimpleClientConfig => {
  return simpleClientConfigurations[clientId] || {
    clientId: clientId,
    adminId: "default",
    name: "Default Client",
    enabledTabs: ["overview", "environment", "environment-new"],
    features: {
      sidebar: false,
      filters: true,
      drilldown: true,
      locationFilter: true
    }
  };
};

// Function to check if a tab is enabled for a client
export const isTabEnabled = (clientId: string, tabId: string): boolean => {
  const config = getSimpleClientConfiguration(clientId);
  return config.enabledTabs.includes(tabId);
};

// Function to get enabled tabs for a client (returns the existing tab structure filtered)
export const getEnabledTabs = (clientId: string) => {
  const config = getSimpleClientConfiguration(clientId);

  // Return the existing tab structure, filtered by enabled tabs
  const allTabs = [
    {
      id: "overview",
      label: "Overview",
      icon: "BarChart3",
      enabled: true,
      order: 1
    },
    {
      id: "environment",
      label: "Environment",
      icon: "Leaf",
      enabled: true,
      order: 2
    },
    {
      id: "environment-new",
      label: "Environment (New)",
      icon: "Leaf",
      enabled: true,
      order: 3
    }
  ];

  return allTabs.filter(tab => config.enabledTabs.includes(tab.id));
};

// Legacy function for backward compatibility
export const getClientConfiguration = (clientId: string): any => {
  const simpleConfig = getSimpleClientConfiguration(clientId);
  return {
    clientId: simpleConfig.clientId,
    adminId: simpleConfig.adminId,
    name: simpleConfig.name,
    features: simpleConfig.features,
    tabs: getEnabledTabs(clientId)
  };
};
