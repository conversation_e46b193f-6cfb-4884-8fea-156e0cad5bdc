# Simple Client Tab Configuration System

This system allows you to show/hide tabs based on client ID (adminId) passed as a URL parameter. It keeps your existing tabs and charts but makes them configurable per client.

## How it Works

1. **URL Parameter**: Pass client data as an encrypted parameter in the URL
2. **Simple Configuration**: Each client has a simple list of enabled tabs
3. **Existing Charts**: Uses your existing chart components and structure
4. **No Sidebar/Header**: Clean UI without sidebar or header bars

## URL Format

```
https://yourapp.com/?encryptedClientData
```

The encrypted data should contain:
```json
{
  "userId": "101",
  "adminId": "289"
}
```

**Note**: The system uses `adminId` for client identification (as per your requirement that tabs are valid only for adminId 289).

## Example Client Configurations

### Admin 289 - Full Access (Main Valid Admin)
- All tabs enabled: Overview, Environment, Environment (New)
- All features enabled: filters, drilldown, location filter
- All existing charts visible

### Admin 291 - Full Access
- All tabs enabled: Overview, Environment, Environment (New)
- All features enabled: filters, drilldown, location filter
- All existing charts visible

### Admin 292 - Environment Only
- Only Environment and Environment (New) tabs
- Filters enabled but no drilldown or location filter
- Limited to environment-related charts

### Admin 293 - Overview Only
- Only Overview tab
- No filters, no drilldown, no location filter
- Basic overview charts only

## Adding a New Client

1. **Add to clientConfigurations.ts**:
```typescript
"104": {
  clientId: "104",
  adminId: "294",
  name: "Custom Client",
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: "BarChart3",
      enabled: true,
      order: 1,
      sections: [
        {
          id: "energy-emissions",
          title: "Energy and Emissions",
          enabled: true,
          order: 1,
          charts: [
            { id: "scope-emissions", component: "ScopeEmissionsBarChart", enabled: true, order: 1 }
          ]
        }
      ]
    }
  ],
  features: {
    sidebar: false,
    filters: true,
    drilldown: false,
    locationFilter: false
  }
}
```

2. **Test the URL**:
```javascript
// Encrypt the client data
const clientData = { userId: 104, adminId: 294 };
const encrypted = encryptNumber(JSON.stringify(clientData), SECRET_KEY);
// Use: https://yourapp.com/?{encrypted}
```

## Available Charts

- `ScopeEmissionsBarChart`
- `EnvironmentScopeEmissionsBarChart`
- `Scope3EmissionsByLocationChart`
- `EnvironmentScope3EmissionsByLocationChart`
- `RenewableNonRenewableEnergyChart`
- `RenewableEnergyBreakdownChart`
- `NonRenewableEnergyBreakdownChart`
- `WasteManagementChart`
- `HazardousWasteDisposedChart`
- `NonHazardousWasteDisposedChart`
- `WaterWithdrawalChart`
- `WaterConsumptionChart`
- `WaterDischargeChart`
- `SafetyLTIFChart`
- `EnergyEmissionsRatingsChart`
- `Scope2EmissionsNalagarhChart`
- `Scope1EmissionsHosurChart`
- `Scope2EmissionsHosurChart`

## Features Configuration

- `sidebar`: Show/hide the sidebar navigation
- `filters`: Enable/disable filter controls
- `drilldown`: Enable/disable chart drilldown functionality
- `locationFilter`: Enable/disable location-based filtering

## Section Layouts

The system supports different layouts for sections:
- **Standard**: Charts stacked vertically
- **Grid**: Charts in a 2-column grid (for breakdown sections)
- **Custom**: Special handling for water stewardship sections

## Testing Different Clients

To test different client configurations, use the browser console:

1. **Admin 289 (Main Valid Admin - Full Access)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 101, adminId: 289 }), "e!sq6esgdash1"))
   ```

2. **Admin 291 (Full Access)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 101, adminId: 291 }), "e!sq6esgdash1"))
   ```

3. **Admin 292 (Environment Only)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 102, adminId: 292 }), "e!sq6esgdash1"))
   ```

4. **Admin 293 (Overview Only)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 103, adminId: 293 }), "e!sq6esgdash1"))
   ```

Use the encrypted output as the URL parameter to test different configurations.

## Key Features

- ✅ **No Sidebar/Header**: Clean UI as requested
- ✅ **Existing Tabs**: Uses your current tab structure (Overview, Environment, Environment New)
- ✅ **Existing Charts**: All your existing chart components work as before
- ✅ **Client-based Filtering**: Tabs show/hide based on client configuration
- ✅ **Feature Control**: Filters, drilldown, and location filtering can be enabled/disabled per client
- ✅ **AdminId Based**: Uses adminId for client identification (valid for 289)

## Adding New Clients

To add a new client, simply add to `src/config/clientConfigurations.ts`:

```typescript
"294": {
  clientId: "294",
  adminId: "294",
  name: "Custom Client",
  enabledTabs: ["overview"], // Only show overview tab
  features: {
    sidebar: false,
    filters: false,
    drilldown: false,
    locationFilter: false
  }
}
```
