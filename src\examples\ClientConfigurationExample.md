# Modular Client Configuration System

This system allows you to configure different tabs, charts, and features for different clients based on their ID passed as a URL parameter.

## How it Works

1. **URL Parameter**: Pass client data as an encrypted parameter in the URL
2. **Client Configuration**: Each client has a configuration that defines which tabs and charts they can see
3. **Modular Rendering**: The system dynamically renders only the enabled components

## URL Format

```
https://yourapp.com/?encryptedClientData
```

The encrypted data should contain:
```json
{
  "userId": "101",
  "adminId": "291"
}
```

## Example Client Configurations

### Client 101 - Full Access
- All tabs enabled (Overview, Environment, Environment New)
- All features enabled (sidebar, filters, drilldown, location filter)
- All charts visible

### Client 102 - Environment Only
- Only Environment and Environment (New) tabs
- No sidebar
- Filters enabled but no drilldown
- Limited chart access

### Client 103 - Overview Only
- Only Overview tab
- No sidebar, no filters, no drilldown
- Only basic charts (Scope Emissions, Renewable Energy)

## Adding a New Client

1. **Add to clientConfigurations.ts**:
```typescript
"104": {
  clientId: "104",
  adminId: "294",
  name: "Custom Client",
  tabs: [
    {
      id: "overview",
      label: "Overview",
      icon: "BarChart3",
      enabled: true,
      order: 1,
      sections: [
        {
          id: "energy-emissions",
          title: "Energy and Emissions",
          enabled: true,
          order: 1,
          charts: [
            { id: "scope-emissions", component: "ScopeEmissionsBarChart", enabled: true, order: 1 }
          ]
        }
      ]
    }
  ],
  features: {
    sidebar: false,
    filters: true,
    drilldown: false,
    locationFilter: false
  }
}
```

2. **Test the URL**:
```javascript
// Encrypt the client data
const clientData = { userId: 104, adminId: 294 };
const encrypted = encryptNumber(JSON.stringify(clientData), SECRET_KEY);
// Use: https://yourapp.com/?{encrypted}
```

## Available Charts

- `ScopeEmissionsBarChart`
- `EnvironmentScopeEmissionsBarChart`
- `Scope3EmissionsByLocationChart`
- `EnvironmentScope3EmissionsByLocationChart`
- `RenewableNonRenewableEnergyChart`
- `RenewableEnergyBreakdownChart`
- `NonRenewableEnergyBreakdownChart`
- `WasteManagementChart`
- `HazardousWasteDisposedChart`
- `NonHazardousWasteDisposedChart`
- `WaterWithdrawalChart`
- `WaterConsumptionChart`
- `WaterDischargeChart`
- `SafetyLTIFChart`
- `EnergyEmissionsRatingsChart`
- `Scope2EmissionsNalagarhChart`
- `Scope1EmissionsHosurChart`
- `Scope2EmissionsHosurChart`

## Features Configuration

- `sidebar`: Show/hide the sidebar navigation
- `filters`: Enable/disable filter controls
- `drilldown`: Enable/disable chart drilldown functionality
- `locationFilter`: Enable/disable location-based filtering

## Section Layouts

The system supports different layouts for sections:
- **Standard**: Charts stacked vertically
- **Grid**: Charts in a 2-column grid (for breakdown sections)
- **Custom**: Special handling for water stewardship sections

## Testing Different Clients

To test different client configurations:

1. **Client 101 (Full Access)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 101, adminId: 291 }), "e!sq6esgdash1"))
   ```

2. **Client 102 (Environment Only)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 102, adminId: 292 }), "e!sq6esgdash1"))
   ```

3. **Client 103 (Overview Only)**:
   ```javascript
   console.log(encryptNumber(JSON.stringify({ userId: 103, adminId: 293 }), "e!sq6esgdash1"))
   ```

Use the encrypted output as the URL parameter to test different configurations.
