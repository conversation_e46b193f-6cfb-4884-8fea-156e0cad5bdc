declare module "@/data/environment-emissions-data.json" {
  interface EnvironmentEmissionsData {
    [region: string]: {
      scope1: number[];
      scope2: number[];
      scope3: number[];
    };
  }

  interface EnvironmentScope3Data {
    [region: string]: {
      [category: string]: number[];
    };
  }

  interface EnvironmentData {
    environmentEmissionsData: EnvironmentEmissionsData;
    environmentScope3Data: EnvironmentScope3Data;
  }

  const data: EnvironmentData;
  export default data;
}

// Client Configuration Types
export interface TabConfig {
  id: string;
  label: string;
  icon: string;
  enabled: boolean;
  order: number;
  sections?: SectionConfig[];
}

export interface SectionConfig {
  id: string;
  title: string;
  description?: string;
  enabled: boolean;
  charts: ChartConfig[];
  order: number;
}

export interface ChartConfig {
  id: string;
  component: string;
  enabled: boolean;
  props?: Record<string, any>;
  order: number;
}

export interface ClientConfig {
  clientId: string;
  adminId: string;
  name: string;
  tabs: TabConfig[];
  features: {
    sidebar: boolean;
    filters: boolean;
    drilldown: boolean;
    locationFilter: boolean;
  };
  theme?: {
    primaryColor?: string;
    logo?: string;
  };
}

export interface ClientData {
  login_data: string | null;
  admin_data: string | null;
}