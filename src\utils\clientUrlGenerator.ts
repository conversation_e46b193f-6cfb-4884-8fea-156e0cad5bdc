import crypto from "crypto-js";

const SECRET_KEY = "e!sq6esgdash1";

/**
 * Encrypts client data for URL parameter
 */
export function encryptClientData(userId: number, adminId: number): string {
  const data = { userId, adminId };
  return crypto.AES.encrypt(JSON.stringify(data), SECRET_KEY).toString();
}

/**
 * Decrypts client data from URL parameter
 */
export function decryptClientData(encrypted: string): { userId: number; adminId: number } {
  const bytes = crypto.AES.decrypt(encrypted, SECRET_KEY);
  const originalText = bytes.toString(crypto.enc.Utf8);
  return JSON.parse(originalText);
}

/**
 * Generates a complete URL with encrypted client data
 */
export function generateClientUrl(userId: number, adminId: number, baseUrl: string = window.location.origin): string {
  const encrypted = encryptClientData(userId, adminId);
  return `${baseUrl}/?${encrypted}`;
}

/**
 * Pre-configured client URLs for testing
 */
export const testClientUrls = {
  fullAccess: () => generateClientUrl(101, 291),
  environmentOnly: () => generateClientUrl(102, 292),
  overviewOnly: () => generateClientUrl(103, 293),
};

/**
 * Console helper for generating test URLs
 */
export function logTestUrls() {
  console.log("=== Test Client URLs ===");
  console.log("Full Access (Client 101):", testClientUrls.fullAccess());
  console.log("Environment Only (Client 102):", testClientUrls.environmentOnly());
  console.log("Overview Only (Client 103):", testClientUrls.overviewOnly());
  console.log("========================");
}

/**
 * Console helper for generating encrypted parameters
 */
export function logEncryptedParams() {
  console.log("=== Encrypted Parameters ===");
  console.log("Client 101:", encryptClientData(101, 291));
  console.log("Client 102:", encryptClientData(102, 292));
  console.log("Client 103:", encryptClientData(103, 293));
  console.log("============================");
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).clientUrlGenerator = {
    encryptClientData,
    decryptClientData,
    generateClientUrl,
    testClientUrls,
    logTestUrls,
    logEncryptedParams
  };
}
