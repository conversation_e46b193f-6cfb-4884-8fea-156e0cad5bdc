import React from "react";
import { ScopeEmissionsBarChart } from "@/components/charts/ScopeEmissionsBarChart";
import { EnvironmentScopeEmissionsBarChart } from "@/components/charts/EnvironmentScopeEmissionsBarChart";
import { Scope3EmissionsByLocationChart } from "@/components/charts/Scope3EmissionsByLocationChart";
import { EnvironmentScope3EmissionsByLocationChart } from "@/components/charts/EnvironmentScope3EmissionsByLocationChart";
import { RenewableNonRenewableEnergyChart } from "@/components/charts/RenewableNonRenewableEnergyChart";
import { RenewableEnergyBreakdownChart } from "@/components/charts/RenewableEnergyBreakdownChart";
import { NonRenewableEnergyBreakdownChart } from "@/components/charts/NonRenewableEnergyBreakdownChart";
import { WasteManagementChart } from "@/components/charts/WasteManagementChart";
import { HazardousWasteDisposedChart } from "@/components/charts/HazardousWasteDisposedChart";
import { NonHazardousWasteDisposedChart } from "@/components/charts/NonHazardousWasteDisposedChart";
import { WaterWithdrawalChart } from "@/components/charts/WaterWithdrawalChart";
import { WaterConsumptionChart } from "@/components/charts/WaterConsumptionChart";
import { WaterDischargeChart } from "@/components/charts/WaterDischargeChart";
import { SafetyLTIFChart } from "@/components/charts/SafetyLTIFChart";
import { 
  EnergyEmissionsRatingsChart, 
  Scope2EmissionsNalagarhChart, 
  Scope1EmissionsHosurChart, 
  Scope2EmissionsHosurChart 
} from "@/components/charts/EnergyEmissionsRatingsChart";
import { getEnabledSections, getEnabledCharts, getClientConfiguration } from "@/config/clientConfigurations";
import { ChartConfig, SectionConfig } from "@/types/environment-data";

interface ModularChartRendererProps {
  clientId: string;
  activeView: string;
  supplierData: any[];
  locationData: any[];
}

// Chart component mapping
const chartComponents = {
  ScopeEmissionsBarChart,
  EnvironmentScopeEmissionsBarChart,
  Scope3EmissionsByLocationChart,
  EnvironmentScope3EmissionsByLocationChart,
  RenewableNonRenewableEnergyChart,
  RenewableEnergyBreakdownChart,
  NonRenewableEnergyBreakdownChart,
  WasteManagementChart,
  HazardousWasteDisposedChart,
  NonHazardousWasteDisposedChart,
  WaterWithdrawalChart,
  WaterConsumptionChart,
  WaterDischargeChart,
  SafetyLTIFChart,
  EnergyEmissionsRatingsChart,
  Scope2EmissionsNalagarhChart,
  Scope1EmissionsHosurChart,
  Scope2EmissionsHosurChart,
};

const ModularChartRenderer: React.FC<ModularChartRendererProps> = ({
  clientId,
  activeView,
  supplierData,
  locationData
}) => {
  const clientConfig = getClientConfiguration(clientId);
  const enabledSections = getEnabledSections(clientId, activeView);

  const renderChart = (chartConfig: ChartConfig, sectionId: string) => {
    const ChartComponent = chartComponents[chartConfig.component as keyof typeof chartComponents];
    
    if (!ChartComponent) {
      console.warn(`Chart component ${chartConfig.component} not found`);
      return null;
    }

    // Get default props based on chart type and client configuration
    const defaultProps = getDefaultPropsForChart(chartConfig.id, clientConfig, supplierData, activeView);
    const mergedProps = { ...defaultProps, ...chartConfig.props };

    return (
      <div key={chartConfig.id} className="chart-container">
        <ChartComponent {...mergedProps} />
      </div>
    );
  };

  const renderSection = (section: SectionConfig) => {
    const enabledCharts = getEnabledCharts(clientId, activeView, section.id);
    
    if (enabledCharts.length === 0) {
      return null;
    }

    return (
      <div key={section.id} className="section-container mt-16">
        {section.title && (
          <div className="mb-6">
            <h3 className="text-2xl font-bold text-gray-900 mb-1">
              {section.title}
            </h3>
            {section.description && (
              <p className="text-gray-600 mb-4">
                {section.description}
              </p>
            )}
          </div>
        )}
        
        {/* Handle special layouts for certain sections */}
        {section.id === "energy-emissions" && section.title?.includes("Breakdown") ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {enabledCharts.map((chart) => (
              <div key={chart.id}>
                <h4 className="text-lg font-semibold text-gray-800 mb-2">
                  {getChartTitle(chart.id)}
                </h4>
                {renderChart(chart, section.id)}
              </div>
            ))}
          </div>
        ) : section.id === "water-stewardship" && enabledCharts.length > 1 ? (
          <div>
            {/* Render first chart (usually water withdrawal) separately */}
            {enabledCharts.slice(0, 1).map((chart) => renderChart(chart, section.id))}
            
            {/* Render remaining charts in grid if there are more than 1 */}
            {enabledCharts.length > 1 && (
              <div className="mt-16">
                <h3 className="text-2xl font-bold text-gray-900 mb-6">
                  Water Consumption and Water Discharge
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {enabledCharts.slice(1).map((chart) => (
                    <div key={chart.id}>
                      <h4 className="text-lg font-semibold text-gray-800 mb-2">
                        {getChartTitle(chart.id)}
                      </h4>
                      {renderChart(chart, section.id)}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-8">
            {enabledCharts.map((chart) => renderChart(chart, section.id))}
          </div>
        )}
      </div>
    );
  };

  // Special handling for environment-new tab
  if (activeView === "environment-new") {
    const sections = enabledSections;
    return (
      <div className="mt-10">
        {sections.map((section) => {
          const charts = getEnabledCharts(clientId, activeView, section.id);
          return (
            <div key={section.id} className="space-y-4">
              {charts.map((chart) => renderChart(chart, section.id))}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div>
      {enabledSections.map((section) => renderSection(section))}
    </div>
  );
};

// Helper function to get default props for charts
function getDefaultPropsForChart(chartId: string, clientConfig: any, supplierData: any[], activeView: string) {
  const baseProps = {
    region: "Global",
    enableDrilldown: clientConfig.features.drilldown,
    useLocationFilter: clientConfig.features.locationFilter,
  };

  switch (chartId) {
    case "scope-emissions":
    case "environment-scope-emissions":
      return {
        ...baseProps,
        suppliersData: supplierData.filter(
          (item) => item.indicatorId !== 202 && item.indicatorId !== 800 && item.indicatorId !== 490
        ),
      };
    
    case "scope3-location":
    case "environment-scope3-location":
      return {
        ...baseProps,
        suppliersData: supplierData.filter(
          (item) => ![202, 800, 161, 162, 163, 490].includes(item.indicatorId)
        ),
      };
    
    case "renewable-energy":
    case "renewable-breakdown":
    case "non-renewable-breakdown":
      return {
        ...baseProps,
        suppliersData: supplierData.filter((item) => item.indicatorId === 163),
        enableDrilldown: activeView === "environment",
        useLocationFilter: activeView === "environment",
      };
    
    case "water-withdrawal":
      return {
        ...baseProps,
        suppliersData: supplierData.filter((item) => item.indicatorId === 202),
        enableDrilldown: activeView === "environment",
      };
    
    case "water-consumption":
      return {
        ...baseProps,
        suppliersData: supplierData.filter((item) => item.indicatorId === 800),
      };
    
    case "water-discharge":
      return {
        ...baseProps,
        suppliersData: supplierData.filter((item) => item.indicatorId === 490),
      };
    
    default:
      return baseProps;
  }
}

// Helper function to get chart titles
function getChartTitle(chartId: string): string {
  const titles = {
    "renewable-breakdown": "Renewable Energy Consumption",
    "non-renewable-breakdown": "Non-Renewable Energy Consumption",
    "water-consumption": "Water Consumption",
    "water-discharge": "Water Discharge",
  };
  
  return titles[chartId as keyof typeof titles] || "";
}

export default ModularChartRenderer;
